// Auto-Clicker Program - Ekran kapanmasını engellemek için
// X tuşuna basınca 3 saniye aralıklarla sol tıklama yapar

#include <iostream>
#include <windows.h>
#include <thread>
#include <atomic>
#include <conio.h>

class AutoClicker {
private:
    std::atomic<bool> isRunning{ false };
    std::atomic<bool> shouldExit{ false };
    std::thread clickThread;

    void clickLoop() {
        while (isRunning && !shouldExit) {
            // Sol fare tuşuna tıklama
            mouse_event(MOUSEEVENTF_LEFTDOWN, 0, 0, 0, 0);
            Sleep(50); // Kısa bekleme
            mouse_event(MOUSEEVENTF_LEFTUP, 0, 0, 0, 0);

            std::cout << "Tiklama yapildi... (Saat: " << GetTickCount() / 1000 << "s)" << std::endl;

            // 3 saniye bekle
            for (int i = 0; i < 30 && isRunning && !shouldExit; i++) {
                Sleep(100); // 100ms'lik parçalarda bekle (daha responsive)
            }
        }
    }

public:
    void start() {
        if (!isRunning) {
            isRunning = true;
            clickThread = std::thread(&AutoClicker::clickLoop, this);
            std::cout << "Auto-clicker BASLATILDI! (X tusuna tekrar basarak durdurun)" << std::endl;
        }
    }

    void stop() {
        if (isRunning) {
            isRunning = false;
            if (clickThread.joinable()) {
                clickThread.join();
            }
            std::cout << "Auto-clicker DURDURULDU!" << std::endl;
        }
    }

    void exit() {
        shouldExit = true;
        stop();
    }

    bool getRunningStatus() const {
        return isRunning;
    }

    bool getShouldExit() const {
        return shouldExit;
    }
};

int main() {
    AutoClicker clicker;

    std::cout << "=== AUTO-CLICKER PROGRAMI ===" << std::endl;
    std::cout << "Ekran kapanmasini engellemek icin tasarlanmistir." << std::endl;
    std::cout << std::endl;
    std::cout << "Kontroller:" << std::endl;
    std::cout << "X veya x = Baslatma/Durdurma" << std::endl;
    std::cout << "Q veya q = Cikis" << std::endl;
    std::cout << "ESC = Cikis" << std::endl;
    std::cout << std::endl;
    std::cout << "Program hazir! Bir tusa basin..." << std::endl;

    while (!clicker.getShouldExit()) {
        if (_kbhit()) { // Klavye tuşu basıldı mı kontrol et
            char key = _getch();

            switch (key) {
            case 'x':
            case 'X':
                if (clicker.getRunningStatus()) {
                    clicker.stop();
                } else {
                    clicker.start();
                }
                break;

            case 'q':
            case 'Q':
            case 27: // ESC tuşu
                std::cout << "Program sonlandiriliyor..." << std::endl;
                clicker.exit();
                break;

            default:
                std::cout << "Gecersiz tus! (X=basla/dur, Q=cikis)" << std::endl;
                break;
            }
        }

        Sleep(50); // CPU kullanımını azaltmak için kısa bekleme
    }

    std::cout << "Program sonlandi. Herhangi bir tusa basin..." << std::endl;
    _getch();
    return 0;
}
